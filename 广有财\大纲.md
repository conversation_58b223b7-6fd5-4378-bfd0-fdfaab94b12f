# 商业验证和机会分析
1.我有一个需求
2.技术可行性验证
3.做个展示界面
4.调研-这是一个什么生意
5.预判-机会在哪里
6.算账- 生意模式是怎么个样

# 营销先行策略
7.倒着拉清单 - 复杂项目如何推进
8.卖什么做什么  - 先搞明白如何营销,再倒推做产品 (我要怎么卖，倒推做什么产品")
9.如何设计营销内容
10.如何设计PLG --产品驱动增长
     ---用户登录系统通过关注微信公众号
     ---套餐购买环节-加私域送时长
     --- 广告内容可分享裂变


# 产品规划
11.完成原型 - 用AI完成原型先做视觉 再做功能
12.功能拆分 - 用AI来拆分


# 技术实现
13.APK端开发 - AI开发autoxjs代码
    → 教非技术人员如何描述需求让AI写出可用的移动端代码

14.前端开发 - AI开发前端代码
      → 教非技术人员如何描述需求让AI写出可用的移动端代码

15.后端开发 - 把JSON server的代码替换为后端代码
16.低成本部署- serveless数据库+serveless函数 